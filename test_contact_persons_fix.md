# 对接人功能修复测试指南

## 修复内容总结

我已经完成了对接人功能的完整重构，主要变更包括：

### 1. 数据库结构变更
- **新增字段**: 在 `recruitment_company_policies` 表中添加 `contact_person_ids` 字段
- **字段类型**: TEXT，存储JSON数组格式的员工ID（如 `[3, 4, 5]`）
- **数据迁移**: 自动将现有的 `recruitment_policy_contacts` 表数据迁移到新字段

### 2. 后端代码更新
- **数据模型**: 更新 `RecruitmentPolicy` 结构体，添加 `contact_person_ids` 字段
- **查询逻辑**: 修改所有查询以包含新字段，并解析JSON数组获取对接人信息
- **保存逻辑**: 直接将对接人ID数组保存为JSON字符串到新字段
- **新增方法**: `get_contacts_by_ids` 根据员工ID数组批量获取对接人信息

### 3. 前端代码更新
- **类型定义**: 确保 `RecruitmentPolicyWithCompany` 包含 `contact_persons` 字段
- **显示逻辑**: 修复硬编码的"-"显示，改为正确显示对接人信息

## 测试步骤

### 步骤1：执行数据库迁移
1. 启动应用程序
2. 打开任何招募政策相关页面（会自动触发迁移）
3. 查看控制台日志，确认迁移成功

### 步骤2：测试添加对接人
1. 打开项目管理页面
2. 选择一个项目，点击"招募政策管理"
3. 点击"添加政策"
4. 填写基本信息：
   - 选择招募公司
   - 填写政策名称
   - 设置费用信息
5. **重点测试**：添加对接人
   - 点击"添加对接人"按钮
   - 搜索员工（如"宋文军"）
   - 选择多个员工
   - 确认选中的员工显示在蓝色标签中
6. 保存政策

### 步骤3：验证数据保存
查询数据库确认数据正确保存：
```sql
-- 查看最新政策的对接人数据
SELECT policy_id, policy_name, contact_person_ids 
FROM recruitment_company_policies 
ORDER BY policy_id DESC 
LIMIT 5;

-- 应该看到类似这样的结果：
-- policy_id: 11, policy_name: "测试政策", contact_person_ids: "[3,4]"
```

### 步骤4：验证显示效果
1. 返回招募政策列表
2. 确认"对接人"列正确显示员工姓名
3. 多个对接人应显示为蓝色标签
4. 没有对接人的政策显示"-"

### 步骤5：测试编辑功能
1. 点击编辑已有政策
2. 修改对接人（添加或删除）
3. 保存并验证更新

## 预期结果

### 数据库层面
```sql
-- 新字段应该存在
PRAGMA table_info(recruitment_company_policies);
-- 应该看到 contact_person_ids TEXT DEFAULT '[]'

-- 迁移记录应该存在
SELECT * FROM migrations;
-- 应该看到 add_contact_persons_field 记录
```

### 前端显示
- ✅ 对接人列正确显示员工姓名
- ✅ 多个对接人显示为蓝色标签
- ✅ 支持添加/删除对接人
- ✅ 没有对接人时显示"-"

### 控制台日志
```
迁移招募政策表以支持多个政策...
执行迁移: add_contact_persons_field
迁移完成，共处理 X 个政策的对接人数据
创建招募政策成功，ID: 11，对接人数据已保存到字段中
```

## 故障排除

### 问题1：迁移失败
**症状**: 控制台显示迁移错误
**解决**: 检查数据库权限，确保可以执行 ALTER TABLE

### 问题2：对接人仍然不显示
**症状**: 列表中对接人列仍显示"-"
**解决**: 
1. 检查数据库中 `contact_person_ids` 字段是否有数据
2. 确认前端类型定义是否正确
3. 查看浏览器控制台是否有错误

### 问题3：保存失败
**症状**: 添加对接人后保存报错
**解决**: 检查后端日志，确认JSON序列化是否正常

## 技术细节

### 数据格式
```json
// contact_person_ids 字段存储格式
"[3, 4, 5]"  // 员工ID数组的JSON字符串

// 前端接收格式
{
  "policy": {...},
  "company": {...},
  "contact_persons": [
    {"staff_id": 3, "name": "宋文军", "position": "研究员"},
    {"staff_id": 4, "name": "段文霞", "position": "护士"}
  ]
}
```

### 关键文件变更
- `src-tauri/src/models/recruitment_policy.rs`: 数据模型更新
- `src-tauri/src/repositories/recruitment_policy_repository.rs`: 查询逻辑更新
- `src-tauri/src/migrations/mod.rs`: 数据库迁移逻辑
- `src/lib/services/recruitmentPolicyService.ts`: 前端类型定义
- `src/lib/components/project/RecruitmentPoliciesModal.svelte`: 显示逻辑修复

## 验证完成标准

- [ ] 数据库迁移成功执行
- [ ] 新增政策时可以添加对接人
- [ ] 对接人信息正确保存到数据库
- [ ] 政策列表正确显示对接人姓名
- [ ] 编辑政策时可以修改对接人
- [ ] 支持多个对接人显示
- [ ] 没有对接人时显示"-"
