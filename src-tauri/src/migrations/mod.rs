use rusqlite::{Connection, Result};
use log::{info, error};
use std::error::Error;

/// 执行数据库迁移
pub fn run_migrations(conn: &Connection) -> Result<(), Box<dyn Error>> {
    info!("开始执行数据库迁移");
    
    // 创建迁移记录表
    create_migration_table(conn)?;
    
    // 执行各个迁移
    migrate_add_contact_persons_field(conn)?;
    
    info!("数据库迁移完成");
    Ok(())
}

/// 创建迁移记录表
fn create_migration_table(conn: &Connection) -> Result<(), Box<dyn Error>> {
    conn.execute(
        "CREATE TABLE IF NOT EXISTS migrations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            migration_name TEXT UNIQUE NOT NULL,
            executed_at TEXT DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;
    Ok(())
}

/// 检查迁移是否已执行
fn is_migration_executed(conn: &Connection, migration_name: &str) -> Result<bool, Box<dyn Error>> {
    let mut stmt = conn.prepare("SELECT COUNT(*) FROM migrations WHERE migration_name = ?")?;
    let count: i64 = stmt.query_row([migration_name], |row| row.get(0))?;
    Ok(count > 0)
}

/// 记录迁移执行
fn record_migration(conn: &Connection, migration_name: &str) -> Result<(), Box<dyn Error>> {
    conn.execute(
        "INSERT INTO migrations (migration_name) VALUES (?)",
        [migration_name],
    )?;
    Ok(())
}

/// 迁移：为 recruitment_company_policies 表添加对接人字段
fn migrate_add_contact_persons_field(conn: &Connection) -> Result<(), Box<dyn Error>> {
    let migration_name = "add_contact_persons_field";
    
    if is_migration_executed(conn, migration_name)? {
        info!("迁移 {} 已执行，跳过", migration_name);
        return Ok(());
    }
    
    info!("执行迁移: {}", migration_name);
    
    // 添加对接人字段
    conn.execute(
        "ALTER TABLE recruitment_company_policies ADD COLUMN contact_person_ids TEXT DEFAULT '[]'",
        [],
    )?;
    
    // 迁移现有的关联表数据到新字段
    migrate_existing_contact_data(conn)?;
    
    // 记录迁移执行
    record_migration(conn, migration_name)?;
    
    info!("迁移 {} 执行完成", migration_name);
    Ok(())
}

/// 迁移现有的对接人关联数据
fn migrate_existing_contact_data(conn: &Connection) -> Result<(), Box<dyn Error>> {
    info!("开始迁移现有对接人关联数据");
    
    // 查询所有政策的对接人关联
    let mut stmt = conn.prepare(
        "SELECT policy_id, GROUP_CONCAT(staff_id) as staff_ids 
         FROM recruitment_policy_contacts 
         GROUP BY policy_id"
    )?;
    
    let contact_data = stmt.query_map([], |row| {
        let policy_id: i64 = row.get(0)?;
        let staff_ids_str: String = row.get(1)?;
        Ok((policy_id, staff_ids_str))
    })?;
    
    let mut migrated_count = 0;
    
    for data in contact_data {
        let (policy_id, staff_ids_str) = data?;
        
        // 解析员工ID字符串为数组
        let staff_ids: Vec<i64> = staff_ids_str
            .split(',')
            .filter_map(|s| s.trim().parse().ok())
            .collect();
        
        if !staff_ids.is_empty() {
            // 转换为JSON格式
            let json_str = serde_json::to_string(&staff_ids)
                .unwrap_or_else(|_| "[]".to_string());
            
            // 更新政策表
            conn.execute(
                "UPDATE recruitment_company_policies 
                 SET contact_person_ids = ? 
                 WHERE policy_id = ?",
                [&json_str, &policy_id.to_string()],
            )?;
            
            migrated_count += 1;
            info!("迁移政策 {} 的对接人数据: {}", policy_id, json_str);
        }
    }
    
    info!("迁移完成，共处理 {} 个政策的对接人数据", migrated_count);
    Ok(())
}
