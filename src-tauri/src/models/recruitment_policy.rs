use serde::{Deserialize, Serialize};

/// 招募政策数据模型
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RecruitmentPolicy {
    pub policy_id: Option<i64>,
    pub project_id: String,
    pub recruitment_company_item_id: i64,
    pub policy_name: Option<String>,
    pub informed_consent_fee: f64,
    pub randomization_fee: f64,
    pub fee_currency: Option<String>,
    pub payment_method: Option<String>,
    pub description: Option<String>,
    pub is_active: bool,
    pub notes: Option<String>,
    pub created_at: Option<String>,
    pub updated_at: Option<String>,
}

/// 招募政策与公司信息的组合模型
#[derive(Debug, Serialize, Deserialize)]
pub struct RecruitmentPolicyWithCompany {
    pub policy: RecruitmentPolicy,
    pub company: Option<DictionaryItem>,
    pub contact_persons: Vec<ContactPerson>,
}

/// 字典项模型（复用现有的）
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DictionaryItem {
    pub item_id: i64,
    pub item_key: String,
    pub item_value: String,
    pub item_description: Option<String>,
}

/// 联系人信息模型
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ContactPerson {
    pub staff_id: i64,
    pub name: String,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub position: Option<String>,
    pub organization: Option<String>,
}

/// 招募政策联系人关联模型
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RecruitmentPolicyContact {
    pub id: Option<i64>,
    pub policy_id: i64,
    pub staff_id: i64,
    pub created_at: Option<String>,
}

/// 创建招募政策请求
#[derive(Debug, Deserialize, Clone)]
pub struct CreateRecruitmentPolicyRequest {
    pub project_id: String,
    pub recruitment_company_item_id: i64,
    pub policy_name: Option<String>,
    pub informed_consent_fee: f64,
    pub randomization_fee: f64,
    pub fee_currency: Option<String>,
    pub payment_method: Option<String>,
    pub description: Option<String>,
    pub notes: Option<String>,
    pub contact_person_ids: Option<Vec<i64>>,
}

/// 更新招募政策请求
#[derive(Debug, Deserialize, Clone)]
pub struct UpdateRecruitmentPolicyRequest {
    pub recruitment_company_item_id: Option<i64>,
    pub policy_name: Option<String>,
    pub informed_consent_fee: Option<f64>,
    pub randomization_fee: Option<f64>,
    pub fee_currency: Option<String>,
    pub payment_method: Option<String>,
    pub description: Option<String>,
    pub notes: Option<String>,
    pub is_active: Option<bool>,
    pub contact_person_ids: Option<Vec<i64>>,
}

/// 招募政策查询参数
#[derive(Debug, Deserialize)]
pub struct RecruitmentPolicyQuery {
    pub project_id: Option<String>,
    pub recruitment_company_item_id: Option<i64>,
    pub is_active: Option<bool>,
}

/// 招募政策概览查询参数
#[derive(Debug, Deserialize)]
pub struct RecruitmentPolicyOverviewQuery {
    pub project_stage_item_id: Option<i64>,
    pub disease_item_id: Option<i64>,
    pub recruitment_company_item_id: Option<i64>,
    pub is_active: Option<bool>,
    pub page: Option<i64>,
    pub page_size: Option<i64>,
    pub sort_by: Option<String>,
    pub sort_order: Option<String>,
}

/// 招募政策与项目信息的组合模型
#[derive(Debug, Serialize, Deserialize)]
pub struct RecruitmentPolicyWithProject {
    pub policy: RecruitmentPolicy,
    pub company: Option<DictionaryItem>,
    pub project_name: String,
    pub project_short_name: String,
    pub disease: Option<DictionaryItem>,
    pub project_stage: Option<DictionaryItem>,
    pub project_status: Option<DictionaryItem>,
    pub recruitment_status: Option<DictionaryItem>,
    pub contact_persons: Vec<ContactPerson>,
}

/// 招募政策概览分页结果
#[derive(Debug, Serialize, Deserialize)]
pub struct RecruitmentPolicyOverviewPagination {
    pub items: Vec<RecruitmentPolicyWithProject>,
    pub total: i64,
    pub page: i64,
    pub page_size: i64,
}

impl RecruitmentPolicy {
    /// 创建新的招募政策
    pub fn new(request: CreateRecruitmentPolicyRequest) -> Self {
        Self {
            policy_id: None,
            project_id: request.project_id,
            recruitment_company_item_id: request.recruitment_company_item_id,
            policy_name: request.policy_name,
            informed_consent_fee: request.informed_consent_fee,
            randomization_fee: request.randomization_fee,
            fee_currency: request.fee_currency.or_else(|| Some("CNY".to_string())),
            payment_method: request.payment_method,
            description: request.description,
            is_active: true,
            notes: request.notes,
            created_at: None,
            updated_at: None,
        }
    }

    /// 更新招募政策
    pub fn update(&mut self, request: UpdateRecruitmentPolicyRequest) {
        if let Some(company_id) = request.recruitment_company_item_id {
            self.recruitment_company_item_id = company_id;
        }
        if let Some(policy_name) = request.policy_name {
            self.policy_name = Some(policy_name);
        }
        if let Some(informed_fee) = request.informed_consent_fee {
            self.informed_consent_fee = informed_fee;
        }
        if let Some(random_fee) = request.randomization_fee {
            self.randomization_fee = random_fee;
        }
        if let Some(currency) = request.fee_currency {
            self.fee_currency = Some(currency);
        }
        if let Some(payment) = request.payment_method {
            self.payment_method = Some(payment);
        }
        if let Some(desc) = request.description {
            self.description = Some(desc);
        }
        if let Some(notes) = request.notes {
            self.notes = Some(notes);
        }
        if let Some(active) = request.is_active {
            self.is_active = active;
        }
    }

    /// 计算费用差额（随机费用 - 知情费用）
    pub fn calculate_fee_difference(&self) -> f64 {
        self.randomization_fee - self.informed_consent_fee
    }

    /// 验证费用逻辑（随机费用应该大于等于知情费用）
    pub fn validate_fees(&self) -> Result<(), String> {
        if self.randomization_fee < self.informed_consent_fee {
            return Err("随机费用不能小于知情费用".to_string());
        }
        if self.informed_consent_fee < 0.0 {
            return Err("知情费用不能为负数".to_string());
        }
        if self.randomization_fee < 0.0 {
            return Err("随机费用不能为负数".to_string());
        }
        Ok(())
    }
}

impl RecruitmentPolicyWithCompany {
    /// 创建新的组合模型
    pub fn new(policy: RecruitmentPolicy, company: Option<DictionaryItem>) -> Self {
        Self { 
            policy, 
            company, 
            contact_persons: Vec::new() 
        }
    }
    
    /// 创建包含联系人的组合模型
    pub fn with_contacts(policy: RecruitmentPolicy, company: Option<DictionaryItem>, contact_persons: Vec<ContactPerson>) -> Self {
        Self { 
            policy, 
            company, 
            contact_persons 
        }
    }

    /// 获取公司名称
    pub fn get_company_name(&self) -> String {
        self.company
            .as_ref()
            .map(|c| c.item_value.clone())
            .unwrap_or_else(|| "未知公司".to_string())
    }

    /// 计算费用差额
    pub fn calculate_fee_difference(&self) -> f64 {
        self.policy.calculate_fee_difference()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_recruitment_policy_creation() {
        let request = CreateRecruitmentPolicyRequest {
            project_id: "test_project".to_string(),
            recruitment_company_item_id: 1,
            policy_name: Some("测试政策名称".to_string()),
            informed_consent_fee: 500.0,
            randomization_fee: 2000.0,
            fee_currency: Some("CNY".to_string()),
            payment_method: Some("银行转账".to_string()),
            description: Some("测试政策".to_string()),
            notes: Some("测试备注".to_string()),
        };

        let policy = RecruitmentPolicy::new(request);
        
        assert_eq!(policy.project_id, "test_project");
        assert_eq!(policy.recruitment_company_item_id, 1);
        assert_eq!(policy.informed_consent_fee, 500.0);
        assert_eq!(policy.randomization_fee, 2000.0);
        assert_eq!(policy.calculate_fee_difference(), 1500.0);
        assert!(policy.is_active);
    }

    #[test]
    fn test_fee_validation() {
        let mut policy = RecruitmentPolicy {
            policy_id: None,
            project_id: "test".to_string(),
            recruitment_company_item_id: 1,
            informed_consent_fee: 500.0,
            randomization_fee: 2000.0,
            fee_currency: Some("CNY".to_string()),
            payment_method: None,
            description: None,
            is_active: true,
            notes: None,
            created_at: None,
            updated_at: None,
        };

        // 正常情况
        assert!(policy.validate_fees().is_ok());

        // 随机费用小于知情费用
        policy.randomization_fee = 300.0;
        assert!(policy.validate_fees().is_err());

        // 负数费用
        policy.informed_consent_fee = -100.0;
        assert!(policy.validate_fees().is_err());
    }

    #[test]
    fn test_policy_update() {
        let mut policy = RecruitmentPolicy {
            policy_id: Some(1),
            project_id: "test".to_string(),
            recruitment_company_item_id: 1,
            informed_consent_fee: 500.0,
            randomization_fee: 2000.0,
            fee_currency: Some("CNY".to_string()),
            payment_method: None,
            description: None,
            is_active: true,
            notes: None,
            created_at: None,
            updated_at: None,
        };

        let update_request = UpdateRecruitmentPolicyRequest {
            recruitment_company_item_id: Some(2),
            informed_consent_fee: Some(600.0),
            randomization_fee: Some(2500.0),
            fee_currency: None,
            payment_method: Some("支票".to_string()),
            description: Some("更新后的政策".to_string()),
            notes: None,
            is_active: None,
        };

        policy.update(update_request);

        assert_eq!(policy.recruitment_company_item_id, 2);
        assert_eq!(policy.informed_consent_fee, 600.0);
        assert_eq!(policy.randomization_fee, 2500.0);
        assert_eq!(policy.payment_method, Some("支票".to_string()));
        assert_eq!(policy.description, Some("更新后的政策".to_string()));
    }
}
