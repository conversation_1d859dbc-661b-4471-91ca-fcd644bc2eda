use log::{error, info};
use tauri::command;

use crate::models::recruitment_policy::{
    CreateRecruitmentPolicyRequest, DictionaryItem, RecruitmentPolicyQuery,
    RecruitmentPolicyWithCompany, UpdateRecruitmentPolicyRequest,
    RecruitmentPolicyOverviewQuery, RecruitmentPolicyOverviewPagination,
    ContactPerson,
};
use crate::repositories::recruitment_policy_repository::RecruitmentPolicyRepository;

/// 初始化招募政策表
#[command]
pub fn init_recruitment_policy_tables(db_path: String) -> Result<bool, String> {
    info!("初始化招募政策表");

    let repo = RecruitmentPolicyRepository::new(db_path);

    match repo.init_tables() {
        Ok(_) => {
            info!("招募政策表初始化成功");
            Ok(true)
        }
        Err(e) => {
            error!("初始化招募政策表失败: {}", e);
            Err(format!("初始化招募政策表失败: {}", e))
        }
    }
}

/// 迁移招募政策表以支持多个政策
#[command]
pub fn migrate_recruitment_policy_tables(db_path: String) -> Result<bool, String> {
    info!("迁移招募政策表以支持多个政策");

    let repo = RecruitmentPolicyRepository::new(db_path);

    match repo.init_tables() {
        Ok(_) => {
            info!("招募政策表迁移成功");
            Ok(true)
        }
        Err(e) => {
            error!("迁移招募政策表失败: {}", e);
            Err(format!("迁移招募政策表失败: {}", e))
        }
    }
}

/// 获取项目的招募政策列表
#[command]
pub fn get_project_recruitment_policies(
    project_id: String,
    db_path: String,
) -> Result<Vec<RecruitmentPolicyWithCompany>, String> {
    info!("获取项目招募政策列表，项目ID: {}", project_id);

    let repo = RecruitmentPolicyRepository::new(db_path);

    match repo.get_project_policies(&project_id) {
        Ok(policies) => {
            info!("获取项目招募政策成功，数量: {}", policies.len());
            Ok(policies)
        }
        Err(e) => {
            error!("获取项目招募政策失败: {}", e);
            Err(format!("获取项目招募政策失败: {}", e))
        }
    }
}

/// 根据查询条件获取招募政策
#[command]
pub fn get_recruitment_policies(
    query: RecruitmentPolicyQuery,
    db_path: String,
) -> Result<Vec<RecruitmentPolicyWithCompany>, String> {
    info!("获取招募政策列表，查询参数: {:?}", query);

    let repo = RecruitmentPolicyRepository::new(db_path);

    match repo.get_policies(&query) {
        Ok(policies) => {
            info!("获取招募政策成功，数量: {}", policies.len());
            Ok(policies)
        }
        Err(e) => {
            error!("获取招募政策失败: {}", e);
            Err(format!("获取招募政策失败: {}", e))
        }
    }
}

/// 根据ID获取招募政策
#[command]
pub fn get_recruitment_policy_by_id(
    policy_id: i64,
    db_path: String,
) -> Result<Option<RecruitmentPolicyWithCompany>, String> {
    info!("获取招募政策详情，ID: {}", policy_id);

    let repo = RecruitmentPolicyRepository::new(db_path);

    match repo.get_policy_by_id(policy_id) {
        Ok(policy) => {
            if policy.is_some() {
                info!("获取招募政策详情成功");
            } else {
                info!("招募政策不存在，ID: {}", policy_id);
            }
            Ok(policy)
        }
        Err(e) => {
            error!("获取招募政策详情失败: {}", e);
            Err(format!("获取招募政策详情失败: {}", e))
        }
    }
}

/// 创建招募政策
#[command]
pub fn create_recruitment_policy(
    request: CreateRecruitmentPolicyRequest,
    db_path: String,
) -> Result<i64, String> {
    info!("创建招募政策，项目ID: {}, 公司ID: {}", 
          request.project_id, request.recruitment_company_item_id);

    let repo = RecruitmentPolicyRepository::new(db_path);

    match repo.create_policy(&request) {
        Ok(policy_id) => {
            info!("创建招募政策成功，ID: {}", policy_id);
            Ok(policy_id)
        }
        Err(e) => {
            error!("创建招募政策失败: {}", e);
            Err(format!("创建招募政策失败: {}", e))
        }
    }
}

/// 更新招募政策
#[command]
pub fn update_recruitment_policy(
    policy_id: i64,
    request: UpdateRecruitmentPolicyRequest,
    db_path: String,
) -> Result<bool, String> {
    info!("更新招募政策，ID: {}", policy_id);

    let repo = RecruitmentPolicyRepository::new(db_path);

    match repo.update_policy(policy_id, &request) {
        Ok(_) => {
            info!("更新招募政策成功，ID: {}", policy_id);
            Ok(true)
        }
        Err(e) => {
            error!("更新招募政策失败: {}", e);
            Err(format!("更新招募政策失败: {}", e))
        }
    }
}

/// 删除招募政策（软删除）
#[command]
pub fn delete_recruitment_policy(
    policy_id: i64,
    db_path: String,
) -> Result<bool, String> {
    info!("删除招募政策，ID: {}", policy_id);

    let repo = RecruitmentPolicyRepository::new(db_path);

    match repo.delete_policy(policy_id) {
        Ok(_) => {
            info!("删除招募政策成功，ID: {}", policy_id);
            Ok(true)
        }
        Err(e) => {
            error!("删除招募政策失败: {}", e);
            Err(format!("删除招募政策失败: {}", e))
        }
    }
}

/// 硬删除招募政策
#[command]
pub fn hard_delete_recruitment_policy(
    policy_id: i64,
    db_path: String,
) -> Result<bool, String> {
    info!("硬删除招募政策，ID: {}", policy_id);

    let repo = RecruitmentPolicyRepository::new(db_path);

    match repo.hard_delete_policy(policy_id) {
        Ok(_) => {
            info!("硬删除招募政策成功，ID: {}", policy_id);
            Ok(true)
        }
        Err(e) => {
            error!("硬删除招募政策失败: {}", e);
            Err(format!("硬删除招募政策失败: {}", e))
        }
    }
}

/// 获取招募公司列表
#[command]
pub fn get_recruitment_companies(db_path: String) -> Result<Vec<DictionaryItem>, String> {
    info!("获取招募公司列表");

    let repo = RecruitmentPolicyRepository::new(db_path);

    match repo.get_recruitment_companies() {
        Ok(companies) => {
            info!("获取招募公司列表成功，数量: {}", companies.len());
            Ok(companies)
        }
        Err(e) => {
            error!("获取招募公司列表失败: {}", e);
            Err(format!("获取招募公司列表失败: {}", e))
        }
    }
}

/// 批量创建招募政策
#[command]
pub fn batch_create_recruitment_policies(
    requests: Vec<CreateRecruitmentPolicyRequest>,
    db_path: String,
) -> Result<Vec<i64>, String> {
    info!("批量创建招募政策，数量: {}", requests.len());

    let repo = RecruitmentPolicyRepository::new(db_path);
    let mut policy_ids = Vec::new();
    let mut errors = Vec::new();

    for (index, request) in requests.iter().enumerate() {
        match repo.create_policy(request) {
            Ok(policy_id) => {
                policy_ids.push(policy_id);
                info!("批量创建第 {} 个政策成功，ID: {}", index + 1, policy_id);
            }
            Err(e) => {
                let error_msg = format!("第 {} 个政策创建失败: {}", index + 1, e);
                error!("{}", error_msg);
                errors.push(error_msg);
            }
        }
    }

    if !errors.is_empty() {
        return Err(format!("批量创建部分失败: {}", errors.join("; ")));
    }

    info!("批量创建招募政策全部成功，总数: {}", policy_ids.len());
    Ok(policy_ids)
}

/// 批量删除招募政策
#[command]
pub fn batch_delete_recruitment_policies(
    policy_ids: Vec<i64>,
    db_path: String,
) -> Result<bool, String> {
    info!("批量删除招募政策，数量: {}", policy_ids.len());

    let repo = RecruitmentPolicyRepository::new(db_path);
    let mut errors = Vec::new();

    for (index, policy_id) in policy_ids.iter().enumerate() {
        match repo.delete_policy(*policy_id) {
            Ok(_) => {
                info!("批量删除第 {} 个政策成功，ID: {}", index + 1, policy_id);
            }
            Err(e) => {
                let error_msg = format!("第 {} 个政策删除失败 (ID: {}): {}", index + 1, policy_id, e);
                error!("{}", error_msg);
                errors.push(error_msg);
            }
        }
    }

    if !errors.is_empty() {
        return Err(format!("批量删除部分失败: {}", errors.join("; ")));
    }

    info!("批量删除招募政策全部成功，总数: {}", policy_ids.len());
    Ok(true)
}

/// 获取招募政策概览（包含项目信息）
#[command]
pub fn get_recruitment_policies_overview(
    query: RecruitmentPolicyOverviewQuery,
    db_path: String,
) -> Result<RecruitmentPolicyOverviewPagination, String> {
    info!("获取招募政策概览，查询参数: {:?}", query);

    let repo = RecruitmentPolicyRepository::new(db_path);

    match repo.get_policies_overview(&query) {
        Ok(result) => {
            info!("获取招募政策概览成功，数量: {}, 总数: {}", result.items.len(), result.total);
            Ok(result)
        }
        Err(e) => {
            error!("获取招募政策概览失败: {}", e);
            Err(format!("获取招募政策概览失败: {}", e))
        }
    }
}

/// 获取政策的联系人列表
#[command]
pub fn get_policy_contacts(
    policy_id: i64,
    db_path: String,
) -> Result<Vec<ContactPerson>, String> {
    info!("获取政策联系人列表，政策ID: {}", policy_id);

    let repo = RecruitmentPolicyRepository::new(db_path);

    match repo.get_policy_contacts(policy_id) {
        Ok(contacts) => {
            info!("获取政策联系人成功，数量: {}", contacts.len());
            Ok(contacts)
        }
        Err(e) => {
            error!("获取政策联系人失败: {}", e);
            Err(format!("获取政策联系人失败: {}", e))
        }
    }
}

/// 设置政策的联系人（替换现有的）
#[command]
pub fn set_policy_contacts(
    policy_id: i64,
    staff_ids: Vec<i64>,
    db_path: String,
) -> Result<bool, String> {
    info!("设置政策联系人，政策ID: {}，联系人数量: {}", policy_id, staff_ids.len());

    let repo = RecruitmentPolicyRepository::new(db_path);

    match repo.set_policy_contacts(policy_id, &staff_ids) {
        Ok(_) => {
            info!("设置政策联系人成功");
            Ok(true)
        }
        Err(e) => {
            error!("设置政策联系人失败: {}", e);
            Err(format!("设置政策联系人失败: {}", e))
        }
    }
}

/// 添加政策联系人
#[command]
pub fn add_policy_contact(
    policy_id: i64,
    staff_id: i64,
    db_path: String,
) -> Result<bool, String> {
    info!("添加政策联系人，政策ID: {}，员工ID: {}", policy_id, staff_id);

    let repo = RecruitmentPolicyRepository::new(db_path);

    match repo.add_policy_contact(policy_id, staff_id) {
        Ok(_) => {
            info!("添加政策联系人成功");
            Ok(true)
        }
        Err(e) => {
            error!("添加政策联系人失败: {}", e);
            Err(format!("添加政策联系人失败: {}", e))
        }
    }
}

/// 移除政策联系人
#[command]
pub fn remove_policy_contact(
    policy_id: i64,
    staff_id: i64,
    db_path: String,
) -> Result<bool, String> {
    info!("移除政策联系人，政策ID: {}，员工ID: {}", policy_id, staff_id);

    let repo = RecruitmentPolicyRepository::new(db_path);

    match repo.remove_policy_contact(policy_id, staff_id) {
        Ok(_) => {
            info!("移除政策联系人成功");
            Ok(true)
        }
        Err(e) => {
            error!("移除政策联系人失败: {}", e);
            Err(format!("移除政策联系人失败: {}", e))
        }
    }
}
