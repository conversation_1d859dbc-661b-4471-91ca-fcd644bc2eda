<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import Input from '$lib/components/ui/input.svelte';
  import SearchableSelect, { type SelectOption } from '$lib/components/ui/SearchableSelect.svelte';
  import { X } from 'lucide-svelte';
  import {
    type CreateRecruitmentPolicyRequest,
    type UpdateRecruitmentPolicyRequest,
    type RecruitmentPolicyWithProject,
    type DictionaryItem,
    type ContactPerson
  } from '$lib/services/recruitmentPolicyService';
  import { staffService, type Staff } from '$lib/services/staffService';

  // Props
  export let show = false;
  export let mode: 'create' | 'edit' = 'create';
  export let editingPolicy: RecruitmentPolicyWithProject | null = null;
  export let projects: { project: { project_id?: string; project_name: string; project_short_name: string } }[] = [];
  export let recruitmentCompanies: DictionaryItem[] = [];

  // 事件分发器
  const dispatch = createEventDispatcher<{
    close: void;
    save: CreateRecruitmentPolicyRequest | UpdateRecruitmentPolicyRequest;
  }>();

  // 表单数据
  let formData: Partial<CreateRecruitmentPolicyRequest> = {
    project_id: '',
    recruitment_company_item_id: 0,
    policy_name: '',
    informed_consent_fee: 0,
    randomization_fee: 0,
    fee_currency: 'CNY',
    payment_method: '',
    description: '',
    notes: '',
    contact_person_ids: []
  };

  // 表单状态
  let isSubmitting = false;
  let formErrors: Record<string, string> = {};

  // 联系人相关状态
  let allStaff: Staff[] = [];
  let selectedContactPersons: Staff[] = [];
  let contactSearchTerm = '';
  let showContactSearch = false;
  let isLoadingStaff = false;

  // 转换项目数据为选择器选项
  let projectOptions: SelectOption[] = [];
  $: projectOptions = projects.map(projectWithDetails => ({
    value: projectWithDetails.project.project_id || '',
    label: `${projectWithDetails.project.project_name} (${projectWithDetails.project.project_short_name})`,
    searchText: `${projectWithDetails.project.project_name} ${projectWithDetails.project.project_short_name}`
  }));

  // 监听编辑政策变化
  $: if (editingPolicy && mode === 'edit') {
    formData = {
      project_id: editingPolicy.policy.project_id,
      recruitment_company_item_id: editingPolicy.policy.recruitment_company_item_id,
      policy_name: editingPolicy.policy.policy_name || '',
      informed_consent_fee: editingPolicy.policy.informed_consent_fee,
      randomization_fee: editingPolicy.policy.randomization_fee,
      fee_currency: editingPolicy.policy.fee_currency || 'CNY',
      payment_method: editingPolicy.policy.payment_method || '',
      description: editingPolicy.policy.description || '',
      notes: editingPolicy.policy.notes || '',
      contact_person_ids: editingPolicy.contact_persons.map(cp => cp.staff_id)
    };

    // 设置选中的联系人
    selectedContactPersons = editingPolicy.contact_persons.map(cp => ({
      id: cp.staff_id,
      name: cp.name,
      phone: cp.phone || '',
      email: cp.email || '',
      position_item_id: 0, // 这里可以根据需要设置
      gender: '',
      birthday: '',
      isPI: false,
      organization: cp.organization || '',
      position_name: cp.position
    }));
  }

  // 重置表单
  function resetForm() {
    formData = {
      project_id: '',
      recruitment_company_item_id: 0,
      policy_name: '',
      informed_consent_fee: 0,
      randomization_fee: 0,
      fee_currency: 'CNY',
      payment_method: '',
      description: '',
      notes: '',
      contact_person_ids: []
    };
    formErrors = {};
    selectedContactPersons = [];
    contactSearchTerm = '';
    showContactSearch = false;
  }

  // 验证表单
  function validateForm(): boolean {
    formErrors = {};

    if (!formData.project_id) {
      formErrors.project_id = '请选择项目';
    }

    if (!formData.recruitment_company_item_id) {
      formErrors.recruitment_company_item_id = '请选择招募公司';
    }

    if (!formData.policy_name || formData.policy_name.trim() === '') {
      formErrors.policy_name = '请填写政策名称';
    }

    if (formData.informed_consent_fee === undefined || formData.informed_consent_fee < 0) {
      formErrors.informed_consent_fee = '知情费用不能为负数';
    }

    if (formData.randomization_fee === undefined || formData.randomization_fee < 0) {
      formErrors.randomization_fee = '随机费用不能为负数';
    }

    if (formData.randomization_fee !== undefined && formData.informed_consent_fee !== undefined && 
        formData.randomization_fee < formData.informed_consent_fee) {
      formErrors.randomization_fee = '随机费用不能小于知情费用';
    }

    return Object.keys(formErrors).length === 0;
  }

  // 提交表单
  async function handleSubmit(event: Event) {
    event.preventDefault();
    if (!validateForm()) return;

    isSubmitting = true;
    try {
      if (mode === 'create') {
        const createRequest: CreateRecruitmentPolicyRequest = {
          project_id: formData.project_id!,
          recruitment_company_item_id: formData.recruitment_company_item_id!,
          policy_name: formData.policy_name,
          informed_consent_fee: formData.informed_consent_fee!,
          randomization_fee: formData.randomization_fee!,
          fee_currency: formData.fee_currency,
          payment_method: formData.payment_method,
          description: formData.description,
          notes: formData.notes
        };
        dispatch('save', createRequest);
      } else {
        const updateRequest: UpdateRecruitmentPolicyRequest = {
          recruitment_company_item_id: formData.recruitment_company_item_id,
          policy_name: formData.policy_name,
          informed_consent_fee: formData.informed_consent_fee,
          randomization_fee: formData.randomization_fee,
          fee_currency: formData.fee_currency,
          payment_method: formData.payment_method,
          description: formData.description,
          notes: formData.notes
        };
        dispatch('save', updateRequest);
      }
    } finally {
      isSubmitting = false;
    }
  }

  // 关闭模态框
  function handleClose() {
    resetForm();
    dispatch('close');
  }

  // 加载员工列表
  async function loadStaff() {
    if (allStaff.length > 0) return; // 避免重复加载

    isLoadingStaff = true;
    try {
      allStaff = await staffService.getAllStaff();
    } catch (error) {
      console.error('加载员工列表失败:', error);
    } finally {
      isLoadingStaff = false;
    }
  }

  // 搜索员工
  let filteredStaff: Staff[] = [];
  $: filteredStaff = allStaff.filter(staff =>
    staff.name.toLowerCase().includes(contactSearchTerm.toLowerCase()) &&
    !selectedContactPersons.some(selected => selected.id === staff.id)
  );

  // 添加联系人
  function addContactPerson(staff: Staff) {
    if (!selectedContactPersons.some(selected => selected.id === staff.id)) {
      selectedContactPersons = [...selectedContactPersons, staff];
      formData.contact_person_ids = selectedContactPersons.map(cp => cp.id!);
      contactSearchTerm = '';
      showContactSearch = false;
    }
  }

  // 移除联系人
  function removeContactPerson(staffId: number) {
    selectedContactPersons = selectedContactPersons.filter(cp => cp.id !== staffId);
    formData.contact_person_ids = selectedContactPersons.map(cp => cp.id!);
  }

  // 打开联系人搜索
  async function openContactSearch() {
    showContactSearch = true;
    await loadStaff();
  }

  // 监听show变化，重置表单
  $: if (!show) {
    resetForm();
  }
</script>

{#if show}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <!-- 模态框头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h2 class="text-xl font-semibold text-gray-900">
          {mode === 'create' ? '新增招募政策' : '编辑招募政策'}
        </h2>
        <Button variant="ghost" size="sm" onclick={handleClose} class="h-8 w-8 p-0">
          <X class="h-4 w-4" />
        </Button>
      </div>

      <!-- 表单内容 -->
      <form onsubmit={handleSubmit} class="p-6 space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 项目选择 -->
          {#if mode === 'create'}
            <div class="md:col-span-2">
              <label for="project" class="block text-sm font-medium text-gray-700 mb-2">项目 *</label>
              <SearchableSelect
                options={projectOptions}
                bind:value={formData.project_id}
                placeholder="请选择项目"
                searchPlaceholder="搜索项目名称或简称..."
                emptyMessage="暂无项目"
                noResultsMessage="未找到匹配的项目"
                loading={projects.length === 0}
                loadingMessage="加载项目中..."
                required
                on:change={(e) => formData.project_id = e.detail}
              />
              {#if formErrors.project_id}
                <p class="text-red-500 text-xs mt-1">{formErrors.project_id}</p>
              {/if}
            </div>
          {/if}

          <!-- 招募公司选择 -->
          <div class="md:col-span-2">
            <label for="company" class="block text-sm font-medium text-gray-700 mb-2">招募公司 *</label>
            <select
              id="company"
              bind:value={formData.recruitment_company_item_id}
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              class:border-red-500={formErrors.recruitment_company_item_id}
              required
            >
              <option value={0}>请选择招募公司</option>
              {#each recruitmentCompanies as company}
                <option value={company.item_id}>{company.item_value}</option>
              {/each}
            </select>
            {#if formErrors.recruitment_company_item_id}
              <p class="text-red-500 text-xs mt-1">{formErrors.recruitment_company_item_id}</p>
            {/if}
          </div>

          <!-- 政策名称 -->
          <div class="md:col-span-2">
            <label for="policy-name" class="block text-sm font-medium text-gray-700 mb-2">政策名称 *</label>
            <Input
              id="policy-name"
              type="text"
              bind:value={formData.policy_name}
              placeholder="如：2024年标准政策、试验期政策、特殊合作政策等"
              class={formErrors.policy_name ? "border-red-500" : ""}
              required
            />
            {#if formErrors.policy_name}
              <p class="text-red-500 text-xs mt-1">{formErrors.policy_name}</p>
            {/if}
            <p class="text-xs text-gray-500 mt-1">用于区分同一招募公司的不同政策</p>
          </div>

          <!-- 知情费用 -->
          <div>
            <label for="informed-fee" class="block text-sm font-medium text-gray-700 mb-2">知情费用 *</label>
            <Input
              id="informed-fee"
              type="number"
              bind:value={formData.informed_consent_fee}
              min="0"
              step="0.01"
              placeholder="0.00"
              class={formErrors.informed_consent_fee ? "border-red-500" : ""}
              required
            />
            {#if formErrors.informed_consent_fee}
              <p class="text-red-500 text-xs mt-1">{formErrors.informed_consent_fee}</p>
            {/if}
          </div>

          <!-- 随机费用 -->
          <div>
            <label for="random-fee" class="block text-sm font-medium text-gray-700 mb-2">随机费用 *</label>
            <Input
              id="random-fee"
              type="number"
              bind:value={formData.randomization_fee}
              min="0"
              step="0.01"
              placeholder="0.00"
              class={formErrors.randomization_fee ? "border-red-500" : ""}
              required
            />
            {#if formErrors.randomization_fee}
              <p class="text-red-500 text-xs mt-1">{formErrors.randomization_fee}</p>
            {/if}
          </div>

          <!-- 货币类型 -->
          <div>
            <label for="currency" class="block text-sm font-medium text-gray-700 mb-2">货币类型</label>
            <select
              id="currency"
              bind:value={formData.fee_currency}
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="CNY">人民币 (CNY)</option>
              <option value="USD">美元 (USD)</option>
              <option value="EUR">欧元 (EUR)</option>
            </select>
          </div>

          <!-- 支付方式 -->
          <div>
            <label for="payment-method" class="block text-sm font-medium text-gray-700 mb-2">支付方式</label>
            <Input
              id="payment-method"
              type="text"
              bind:value={formData.payment_method}
              placeholder="如：银行转账、支票等"
            />
          </div>

          <!-- 政策描述 -->
          <div class="md:col-span-2">
            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">政策描述</label>
            <textarea
              id="description"
              bind:value={formData.description}
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="详细描述政策内容..."
            ></textarea>
          </div>

          <!-- 备注 -->
          <div class="md:col-span-2">
            <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">备注</label>
            <textarea
              id="notes"
              bind:value={formData.notes}
              rows="2"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="其他备注信息..."
            ></textarea>
          </div>

          <!-- 对接人 -->
          <div class="md:col-span-2">
            <div class="block text-sm font-medium text-gray-700 mb-2">对接人</div>

            <!-- 已选择的联系人 -->
            {#if selectedContactPersons.length > 0}
              <div class="mb-3">
                <div class="flex flex-wrap gap-2">
                  {#each selectedContactPersons as contact}
                    <div class="inline-flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                      <span>{contact.name}</span>
                      {#if contact.position_name}
                        <span class="ml-1 text-blue-600">({contact.position_name})</span>
                      {/if}
                      <button
                        type="button"
                        onclick={() => removeContactPerson(contact.id!)}
                        class="ml-2 text-blue-600 hover:text-blue-800"
                      >
                        <X class="h-3 w-3" />
                      </button>
                    </div>
                  {/each}
                </div>
              </div>
            {/if}

            <!-- 添加联系人按钮 -->
            <div class="relative">
              <Button
                type="button"
                variant="outline"
                onclick={openContactSearch}
                class="w-full justify-center"
              >
                @
              </Button>

              <!-- 联系人搜索下拉框 -->
              {#if showContactSearch}
                <div class="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-10 max-h-60 overflow-hidden">
                  <!-- 搜索框 -->
                  <div class="p-3 border-b border-gray-200">
                    <input
                      type="text"
                      bind:value={contactSearchTerm}
                      placeholder="搜索员工姓名..."
                      class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <!-- 员工列表 -->
                  <div class="max-h-40 overflow-y-auto">
                    {#if isLoadingStaff}
                      <div class="p-3 text-center text-gray-500">加载中...</div>
                    {:else if filteredStaff.length > 0}
                      {#each filteredStaff as staff}
                        <button
                          type="button"
                          onclick={() => addContactPerson(staff)}
                          class="w-full text-left px-3 py-2 hover:bg-gray-100 border-b border-gray-100 last:border-b-0"
                        >
                          <div class="font-medium text-gray-900">{staff.name}</div>
                          {#if staff.position_name}
                            <div class="text-sm text-gray-500">{staff.position_name}</div>
                          {/if}
                          {#if staff.organization}
                            <div class="text-xs text-gray-400">{staff.organization}</div>
                          {/if}
                        </button>
                      {/each}
                    {:else}
                      <div class="p-3 text-center text-gray-500">
                        {contactSearchTerm ? '未找到匹配的员工' : '请输入搜索关键词'}
                      </div>
                    {/if}
                  </div>

                  <!-- 关闭按钮 -->
                  <div class="p-2 border-t border-gray-200">
                    <Button
                      type="button"
                      variant="outline"
                      onclick={() => showContactSearch = false}
                      class="w-full text-sm"
                    >
                      关闭
                    </Button>
                  </div>
                </div>
              {/if}
            </div>

            <p class="text-xs text-gray-500 mt-1">选择负责协调此政策的对接人员</p>
          </div>
        </div>

        <!-- 表单底部按钮 -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <Button type="button" variant="outline" onclick={handleClose}>
            取消
          </Button>
          <Button type="submit" disabled={isSubmitting} class="bg-blue-600 hover:bg-blue-700">
            {#if isSubmitting}
              保存中...
            {:else}
              {mode === 'create' ? '创建政策' : '保存更改'}
            {/if}
          </Button>
        </div>
      </form>
    </div>
  </div>
{/if}
