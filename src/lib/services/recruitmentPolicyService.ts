import { invoke } from '@tauri-apps/api/core';

// 数据库路径常量
const DATABASE_PATH = '/Users/<USER>/我的文档/sqlite/peckbyte.db';

// 类型定义
export interface RecruitmentPolicy {
  policy_id?: number;
  project_id: string;
  recruitment_company_item_id: number;
  policy_name?: string;
  informed_consent_fee: number;
  randomization_fee: number;
  fee_currency?: string;
  payment_method?: string;
  description?: string;
  is_active: boolean;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export interface DictionaryItem {
  item_id: number;
  item_key: string;
  item_value: string;
  item_description?: string;
}

export interface ContactPerson {
  staff_id: number;
  name: string;
  phone?: string;
  email?: string;
  position?: string;
  organization?: string;
}

export interface RecruitmentPolicyWithCompany {
  policy: RecruitmentPolicy;
  company?: DictionaryItem;
  contact_persons?: ContactPerson[];
}

export interface CreateRecruitmentPolicyRequest {
  project_id: string;
  recruitment_company_item_id: number;
  policy_name?: string;
  informed_consent_fee: number;
  randomization_fee: number;
  fee_currency?: string;
  payment_method?: string;
  description?: string;
  notes?: string;
  contact_person_ids?: number[];
}

export interface UpdateRecruitmentPolicyRequest {
  recruitment_company_item_id?: number;
  policy_name?: string;
  informed_consent_fee?: number;
  randomization_fee?: number;
  fee_currency?: string;
  payment_method?: string;
  description?: string;
  notes?: string;
  is_active?: boolean;
  contact_person_ids?: number[];
}

export interface RecruitmentPolicyQuery {
  project_id?: string;
  recruitment_company_item_id?: number;
  is_active?: boolean;
}

export interface RecruitmentPolicyOverviewQuery {
  project_stage_item_id?: number;
  disease_item_id?: number;
  recruitment_company_item_id?: number;
  is_active?: boolean;
  page?: number;
  page_size?: number;
  sort_by?: string;
  sort_order?: string;
}

export interface RecruitmentPolicyWithProject {
  policy: RecruitmentPolicy;
  company?: DictionaryItem;
  project_name: string;
  project_short_name: string;
  disease?: DictionaryItem;
  project_stage?: DictionaryItem;
  project_status?: DictionaryItem;
  recruitment_status?: DictionaryItem;
  contact_persons: ContactPerson[];
}

export interface RecruitmentPolicyOverviewPagination {
  items: RecruitmentPolicyWithProject[];
  total: number;
  page: number;
  page_size: number;
}

/**
 * 招募政策服务类
 */
export class RecruitmentPolicyService {
  /**
   * 初始化招募政策表
   */
  async initTables(): Promise<boolean> {
    try {
      console.log('初始化招募政策表...');
      return await invoke<boolean>('init_recruitment_policy_tables', {
        dbPath: DATABASE_PATH
      });
    } catch (error: any) {
      console.error('初始化招募政策表失败:', error);
      throw new Error(`初始化招募政策表失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 迁移招募政策表以支持多个政策
   */
  async migrateTables(): Promise<boolean> {
    try {
      console.log('迁移招募政策表以支持多个政策...');
      return await invoke<boolean>('migrate_recruitment_policy_tables', {
        dbPath: DATABASE_PATH
      });
    } catch (error: any) {
      console.error('迁移招募政策表失败:', error);
      throw new Error(`迁移招募政策表失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 获取项目的招募政策列表
   */
  async getProjectPolicies(projectId: string): Promise<RecruitmentPolicyWithCompany[]> {
    try {
      console.log('获取项目招募政策列表:', projectId);
      return await invoke<RecruitmentPolicyWithCompany[]>('get_project_recruitment_policies', {
        projectId,
        dbPath: DATABASE_PATH
      });
    } catch (error: any) {
      console.error('获取项目招募政策列表失败:', error);
      throw new Error(`获取项目招募政策列表失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 根据查询条件获取招募政策
   */
  async getPolicies(query: RecruitmentPolicyQuery): Promise<RecruitmentPolicyWithCompany[]> {
    try {
      console.log('获取招募政策列表:', query);
      return await invoke<RecruitmentPolicyWithCompany[]>('get_recruitment_policies', {
        query,
        dbPath: DATABASE_PATH
      });
    } catch (error: any) {
      console.error('获取招募政策列表失败:', error);
      throw new Error(`获取招募政策列表失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 根据ID获取招募政策
   */
  async getPolicyById(policyId: number): Promise<RecruitmentPolicyWithCompany | null> {
    try {
      console.log('获取招募政策详情:', policyId);
      return await invoke<RecruitmentPolicyWithCompany | null>('get_recruitment_policy_by_id', {
        policyId,
        dbPath: DATABASE_PATH
      });
    } catch (error: any) {
      console.error('获取招募政策详情失败:', error);
      throw new Error(`获取招募政策详情失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 创建招募政策
   */
  async createPolicy(request: CreateRecruitmentPolicyRequest): Promise<number> {
    try {
      console.log('创建招募政策:', request);
      
      // 验证必填字段
      if (!request.project_id) {
        throw new Error('项目ID不能为空');
      }
      if (!request.recruitment_company_item_id) {
        throw new Error('请选择招募公司');
      }
      if (!request.policy_name || request.policy_name.trim() === '') {
        throw new Error('请填写政策名称，便于区分不同政策');
      }
      if (request.informed_consent_fee < 0) {
        throw new Error('知情费用不能为负数');
      }
      if (request.randomization_fee < 0) {
        throw new Error('随机费用不能为负数');
      }
      if (request.randomization_fee < request.informed_consent_fee) {
        throw new Error('随机费用不能小于知情费用');
      }

      return await invoke<number>('create_recruitment_policy', {
        request,
        dbPath: DATABASE_PATH
      });
    } catch (error: any) {
      console.error('创建招募政策失败:', error);
      // 提取具体的错误信息，避免重复的前缀
      let errorMessage = error.message || error || '未知错误';
      if (typeof errorMessage === 'string') {
        // 移除重复的"创建招募政策失败:"前缀
        errorMessage = errorMessage.replace(/^创建招募政策失败:\s*/, '');
      }
      throw new Error(errorMessage);
    }
  }

  /**
   * 更新招募政策
   */
  async updatePolicy(policyId: number, request: UpdateRecruitmentPolicyRequest): Promise<boolean> {
    try {
      console.log('更新招募政策:', policyId, request);
      
      // 验证费用逻辑
      if (request.informed_consent_fee !== undefined && request.informed_consent_fee < 0) {
        throw new Error('知情费用不能为负数');
      }
      if (request.randomization_fee !== undefined && request.randomization_fee < 0) {
        throw new Error('随机费用不能为负数');
      }
      if (request.informed_consent_fee !== undefined && 
          request.randomization_fee !== undefined && 
          request.randomization_fee < request.informed_consent_fee) {
        throw new Error('随机费用不能小于知情费用');
      }

      return await invoke<boolean>('update_recruitment_policy', {
        policyId,
        request,
        dbPath: DATABASE_PATH
      });
    } catch (error: any) {
      console.error('更新招募政策失败:', error);
      // 提取具体的错误信息，避免重复的前缀
      let errorMessage = error.message || error || '未知错误';
      if (typeof errorMessage === 'string') {
        // 移除重复的"更新招募政策失败:"前缀
        errorMessage = errorMessage.replace(/^更新招募政策失败:\s*/, '');
      }
      throw new Error(errorMessage);
    }
  }

  /**
   * 删除招募政策（软删除）
   */
  async deletePolicy(policyId: number): Promise<boolean> {
    try {
      console.log('删除招募政策:', policyId);
      return await invoke<boolean>('delete_recruitment_policy', {
        policyId,
        dbPath: DATABASE_PATH
      });
    } catch (error: any) {
      console.error('删除招募政策失败:', error);
      // 提取具体的错误信息，避免重复的前缀
      let errorMessage = error.message || error || '未知错误';
      if (typeof errorMessage === 'string') {
        // 移除重复的"删除招募政策失败:"前缀
        errorMessage = errorMessage.replace(/^删除招募政策失败:\s*/, '');
      }
      throw new Error(errorMessage);
    }
  }

  /**
   * 硬删除招募政策
   */
  async hardDeletePolicy(policyId: number): Promise<boolean> {
    try {
      console.log('硬删除招募政策:', policyId);
      return await invoke<boolean>('hard_delete_recruitment_policy', {
        policyId,
        dbPath: DATABASE_PATH
      });
    } catch (error: any) {
      console.error('硬删除招募政策失败:', error);
      throw new Error(`硬删除招募政策失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 获取招募公司列表
   */
  async getRecruitmentCompanies(): Promise<DictionaryItem[]> {
    try {
      console.log('获取招募公司列表...');
      return await invoke<DictionaryItem[]>('get_recruitment_companies', {
        dbPath: DATABASE_PATH
      });
    } catch (error: any) {
      console.error('获取招募公司列表失败:', error);
      // 提取具体的错误信息，避免重复的前缀
      let errorMessage = error.message || error || '未知错误';
      if (typeof errorMessage === 'string') {
        // 移除重复的"获取招募公司列表失败:"前缀
        errorMessage = errorMessage.replace(/^获取招募公司列表失败:\s*/, '');
      }
      throw new Error(errorMessage);
    }
  }

  /**
   * 批量创建招募政策
   */
  async batchCreatePolicies(requests: CreateRecruitmentPolicyRequest[]): Promise<number[]> {
    try {
      console.log('批量创建招募政策:', requests.length);
      
      // 验证每个请求
      for (const request of requests) {
        if (!request.project_id) {
          throw new Error('项目ID不能为空');
        }
        if (!request.recruitment_company_item_id) {
          throw new Error('请选择招募公司');
        }
        if (request.randomization_fee < request.informed_consent_fee) {
          throw new Error('随机费用不能小于知情费用');
        }
      }

      return await invoke<number[]>('batch_create_recruitment_policies', {
        requests,
        dbPath: DATABASE_PATH
      });
    } catch (error: any) {
      console.error('批量创建招募政策失败:', error);
      throw new Error(`批量创建招募政策失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 批量删除招募政策
   */
  async batchDeletePolicies(policyIds: number[]): Promise<boolean> {
    try {
      console.log('批量删除招募政策:', policyIds);
      return await invoke<boolean>('batch_delete_recruitment_policies', {
        policyIds,
        dbPath: DATABASE_PATH
      });
    } catch (error: any) {
      console.error('批量删除招募政策失败:', error);
      throw new Error(`批量删除招募政策失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 计算费用差额
   */
  calculateFeeDifference(policy: RecruitmentPolicy): number {
    return policy.randomization_fee - policy.informed_consent_fee;
  }

  /**
   * 格式化费用显示
   */
  formatFee(amount: number, currency: string = 'CNY'): string {
    const symbol = currency === 'CNY' ? '¥' : currency === 'USD' ? '$' : currency === 'EUR' ? '€' : currency;
    return `${symbol}${amount.toLocaleString()}`;
  }

  /**
   * 验证费用逻辑
   */
  validateFees(informedFee: number, randomFee: number): { valid: boolean; message?: string } {
    if (informedFee < 0) {
      return { valid: false, message: '知情费用不能为负数' };
    }
    if (randomFee < 0) {
      return { valid: false, message: '随机费用不能为负数' };
    }
    if (randomFee < informedFee) {
      return { valid: false, message: '随机费用不能小于知情费用' };
    }
    return { valid: true };
  }

  /**
   * 获取招募政策概览（包含项目信息）
   */
  async getPoliciesOverview(query: RecruitmentPolicyOverviewQuery): Promise<RecruitmentPolicyOverviewPagination> {
    try {
      console.log('获取招募政策概览:', query);
      return await invoke<RecruitmentPolicyOverviewPagination>('get_recruitment_policies_overview', {
        query,
        dbPath: DATABASE_PATH
      });
    } catch (error: any) {
      console.error('获取招募政策概览失败:', error);
      throw new Error(`获取招募政策概览失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 获取政策的联系人列表
   */
  async getPolicyContacts(policyId: number): Promise<ContactPerson[]> {
    try {
      console.log('获取政策联系人列表:', policyId);
      return await invoke<ContactPerson[]>('get_policy_contacts', {
        policyId,
        dbPath: DATABASE_PATH
      });
    } catch (error: any) {
      console.error('获取政策联系人列表失败:', error);
      throw new Error(`获取政策联系人列表失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 设置政策的联系人（替换现有的）
   */
  async setPolicyContacts(policyId: number, staffIds: number[]): Promise<boolean> {
    try {
      console.log('设置政策联系人:', policyId, staffIds);
      return await invoke<boolean>('set_policy_contacts', {
        policyId,
        staffIds,
        dbPath: DATABASE_PATH
      });
    } catch (error: any) {
      console.error('设置政策联系人失败:', error);
      throw new Error(`设置政策联系人失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 添加政策联系人
   */
  async addPolicyContact(policyId: number, staffId: number): Promise<boolean> {
    try {
      console.log('添加政策联系人:', policyId, staffId);
      return await invoke<boolean>('add_policy_contact', {
        policyId,
        staffId,
        dbPath: DATABASE_PATH
      });
    } catch (error: any) {
      console.error('添加政策联系人失败:', error);
      throw new Error(`添加政策联系人失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 移除政策联系人
   */
  async removePolicyContact(policyId: number, staffId: number): Promise<boolean> {
    try {
      console.log('移除政策联系人:', policyId, staffId);
      return await invoke<boolean>('remove_policy_contact', {
        policyId,
        staffId,
        dbPath: DATABASE_PATH
      });
    } catch (error: any) {
      console.error('移除政策联系人失败:', error);
      throw new Error(`移除政策联系人失败: ${error.message || '未知错误'}`);
    }
  }
}

// 导出单例实例
export const recruitmentPolicyService = new RecruitmentPolicyService();
